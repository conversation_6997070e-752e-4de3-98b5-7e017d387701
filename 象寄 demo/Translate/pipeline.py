"""
翻译流水线主控制器
协调各个处理模块完成图像翻译任务

=== 处理步骤流程说明 ===

整个翻译流水线采用六阶段处理模式，每个阶段负责特定的功能：

阶段1: OCR识别 (OCRProcessor)
├── 使用PaddleOCR进行文字检测和识别
├── 区分中文和非中文文字区域
├── 提取文字的位置、内容、置信度
├── 分析文字的颜色和样式信息
├── 像素级高度统一处理（±2px容忍度，偶数优先）
└── 输出: 中文文字区域列表，包含位置、内容、样式信息

阶段2: 布局分析 (LayoutProcessor)
├── 分析文字区域的空间关系和对齐方式
├── 按Y坐标将文字区域分行处理
├── 检测行内对齐关系（左对齐、居中、右对齐）
├── 识别分布组（同行内的文字分布模式）
├── 跨行对齐检测和分组
└── 输出: 对齐组信息和分布组信息

阶段3: 字体匹配 (FontProcessor)
├── 从图像中提取每个文字区域的样式特征
├── 测量文字的实际像素高度
├── 匹配最适合的字体文件和字体粗细
├── 考虑字体的中文支持情况
└── 输出: 每个区域的字体匹配结果

阶段4: 翻译处理 (TranslationProcessor)
├── 使用翻译字典进行中文到日文的翻译
├── 应用布局分析结果进行智能分组
├── 处理分布组的相对位置保持
├── 处理对齐组的统一约束（10%容忍度）
├── 计算目标字体大小以匹配原文高度
├── 字号缓存机制优化重复计算
└── 输出: 翻译结果列表，包含译文、位置、字体信息

阶段5: 图像修复 (InpaintProcessor)
├── 生成中文文字区域的掩码
├── 使用OpenCV的TELEA算法进行图像修复
├── 去除原始中文文字，为渲染译文做准备
├── 保持背景的连续性和自然性
└── 输出: 去除中文文字后的干净图像

阶段6: 渲染译文 (Renderer)
├── 在修复后的图像上渲染日文译文
├── 应用翻译阶段确定的字体和大小
├── 保持原文的颜色和视觉效果
├── 处理分组的缩放约束和对齐要求
├── 字号缓存机制优化渲染性能
└── 输出: 最终的翻译图像

=== 关键技术特性 ===

1. 像素级高度统一: 在OCR阶段统一相似高度（±2px），偶数优先
2. 智能布局分析: 支持对齐组和分布组的识别
3. 字号缓存机制: 在翻译和渲染阶段缓存字号匹配结果
4. 精确匹配标准: 差值必须为0才算精确匹配
5. 10%容忍度约束: 同组内文字保持相对尺寸一致性
6. 调试功能完整: 支持OCR、布局、翻译各阶段的可视化调试

=== 数据流向 ===

图像文件 → OCR识别 → 布局分析 → 字体匹配 → 翻译处理 → 图像修复 → 渲染译文 → 最终结果

每个阶段都返回ProcessingResult对象，包含处理状态和数据，支持错误处理和流程控制。
"""
import os
import cv2
from typing import Optional, Dict, Any

from models.data_models import ProcessingResult, PipelineConfig
from config.settings import get_config_manager
from processors.ocr_processor import OCRProcessor
from processors.font_processor import FontProcessor
from processors.translation_processor import TranslationProcessor
from processors.inpaint_processor import InpaintProcessor
from processors.renderer import Renderer
from processors.layout_processor import LayoutProcessor
from utils.visualizer import Visualizer


class TranslationPipeline:
    """翻译流水线"""
    
    def __init__(self, font_weight: int = 400, enable_debug: bool = True):
        """初始化翻译流水线"""
        self.config_manager = get_config_manager()
        self.config = self.config_manager.config
        self.enable_debug = enable_debug
        
        # 更新配置
        self.config_manager.update_config(
            default_font_weight=font_weight,
            enable_debug_output=enable_debug
        )
        
        # 初始化处理器
        self._init_processors(font_weight)
        
        print(f"翻译流水线就绪 | 字体:{font_weight} | 调试:{'开' if enable_debug else '关'}")
    
    def _init_processors(self, font_weight: int):
        """初始化所有处理器"""
        self.ocr_processor = OCRProcessor()
        self.font_processor = FontProcessor()
        self.translation_processor = TranslationProcessor(font_weight)
        self.inpaint_processor = InpaintProcessor()
        self.renderer = Renderer(font_weight)
        self.layout_processor = LayoutProcessor()
        
        # 初始化可视化工具
        self.visualizer = Visualizer() if self.enable_debug else None
    
    def process_image(self, image_path: str) -> ProcessingResult:
        """处理图像进行翻译"""
        try:
            # 验证输入
            if not os.path.exists(image_path):
                return ProcessingResult.error_result(f"图像文件不存在: {image_path}")
            
            print(f"\n处理图像: {image_path}")
            self.config_manager.ensure_output_dir()
            
            # 执行翻译流水线
            return self._execute_pipeline(image_path)
            
        except Exception as e:
            error_msg = f"流水线处理失败: {str(e)}"
            print(f"错误: {error_msg}")
            return ProcessingResult.error_result(error_msg)
    
    def _execute_pipeline(self, image_path: str) -> ProcessingResult:
        """执行翻译流水线的核心逻辑"""
        # 阶段1: OCR识别
        print("OCR识别...")
        ocr_result = self.ocr_processor.process_image(image_path, self.config.ocr_confidence_threshold)
        if not ocr_result.success:
            return ocr_result
        
        ocr_data = ocr_result.data
        if len(ocr_data.chinese_regions) == 0:
            return ProcessingResult.success_result({
                'message': '未检测到中文文字',
                'original_image': cv2.imread(image_path)
            })
        
        # 阶段2: 布局分析
        print("布局分析...")
        layout_result = self.layout_processor.analyze_layout(ocr_data.chinese_regions)
        if not layout_result.success:
            return layout_result
        
        # 阶段3: 字体匹配
        print("字体匹配...")
        image = cv2.imread(image_path)
        font_result = self.font_processor.process_regions(image, ocr_data.chinese_regions)
        if not font_result.success:
            return font_result
        
        # 阶段4: 翻译处理
        print("翻译处理...")
        translation_result = self.translation_processor.process_translation(
            image, ocr_data.chinese_regions, font_result.data, layout_result.data
        )
        if not translation_result.success:
            return translation_result
        
        translation_data = translation_result.data
        if len(translation_data) == 0:
            return ProcessingResult.success_result({
                'message': '没有找到可翻译的文字',
                'original_image': image
            })
        
        # 阶段5: 图像修复
        print("图像修复...")
        inpaint_result = self.inpaint_processor.process_inpainting(
            image, ocr_data.chinese_regions
        )
        if not inpaint_result.success:
            return inpaint_result
        
        # 阶段6: 渲染译文
        print("渲染译文...")
        render_result = self.renderer.render_translations(
            inpaint_result.data, translation_data, layout_result.data, image_path
        )
        if not render_result.success:
            return render_result
        
        # 完成处理
        return self._finalize_result(image_path, ocr_data, layout_result.data, 
                                   translation_data, render_result.data)
    
    def _finalize_result(self, image_path: str, ocr_data, layout_result, 
                        translation_data, render_data) -> ProcessingResult:
        """完成处理并生成最终结果"""
        final_image = render_data['image']
        render_log = render_data['render_log']
        
        # 保存最终结果
        final_path = self.renderer.save_final_image(final_image)
        
        # 生成调试图像
        if self.enable_debug and self.visualizer:
            self._generate_debug_images(image_path, ocr_data, layout_result, translation_data)
        
        # 返回结果
        result_data = {
            'final_image': final_image,
            'final_path': final_path,
            'render_log': render_log,
            'ocr_result': ocr_data,
            'layout_result': layout_result,
            'translation_results': translation_data
        }
        
        print(f"处理完成！翻译了 {len(render_log)} 个文字")
        return ProcessingResult.success_result(result_data)
    
    def _generate_debug_images(self, image_path: str, ocr_data, layout_result, translation_data):
        """生成调试图像"""
        try:
            print("生成调试图像...")
            self.visualizer.draw_ocr_detection(image_path, ocr_data)
            self.visualizer.draw_layout_analysis(image_path, layout_result)
            self.visualizer.draw_translation_result(image_path, translation_data)
            print("调试图像生成完成")
        except Exception as e:
            print(f"调试图像生成失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        try:
            self.ocr_processor.cleanup()
            print("流水线资源清理完成")
        except Exception as e:
            print(f"资源清理失败: {e}")
    
    def get_config(self) -> PipelineConfig:
        """获取当前配置"""
        return self.config
    
    def update_config(self, **kwargs):
        """更新配置"""
        # 处理inpaint相关配置
        if 'inpaint_engine' in kwargs or 'inpaint_config' in kwargs:
            engine = kwargs.pop('inpaint_engine', None)
            config = kwargs.pop('inpaint_config', None)

            if engine or config:
                self.config_manager.update_inpaint_config(engine, **(config or {}))

                # 重新初始化inpaint处理器
                if hasattr(self, 'inpaint_processor'):
                    inpaint_config = self.config_manager.get_inpaint_config()
                    # 处理配置格式 - 支持嵌套和扁平化两种格式
                    if 'config' in inpaint_config:
                        # 嵌套格式
                        engine_name = inpaint_config['engine']
                        engine_config = inpaint_config['config']
                    else:
                        # 扁平化格式
                        engine_name = inpaint_config.get('engine', 'lama')
                        engine_config = {k: v for k, v in inpaint_config.items() if k != 'engine'}

                    self.inpaint_processor = InpaintProcessor(
                        engine=engine_name,
                        config=engine_config
                    )

        # 更新其他配置
        self.config_manager.update_config(**kwargs)
        self.config = self.config_manager.config
    
    def get_translation_stats(self) -> Dict[str, Any]:
        """获取翻译统计信息"""
        return {
            'translation_dict_size': len(self.config_manager.translation_dict),
            'font_mapping_size': len(self.config_manager.font_mapping),
            'fonts_dir': self.config_manager.get_fonts_dir(),
            'output_dir': self.config_manager.get_output_dir()
        }
