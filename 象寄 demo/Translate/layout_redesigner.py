"""
AI驱动的电商产品布局重设计系统

主要功能：
1. OCR识别原图文本内容和位置
2. 擦除原始文本区域
3. 调用GPT-4o-mini重新设计布局
4. 严格按照GPT返回的JSON参数渲染新设计
"""
import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import json
import os
import base64
import requests
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass, asdict
import glob


@dataclass
class OriginalText:
    """原始文本信息"""
    text: str
    bbox: Tuple[int, int, int, int]  # (x, y, w, h)
    position_desc: str  # "top-left", "center", etc.
    confidence: float


@dataclass  
class DesignElement:
    """设计元素"""
    text: str
    position: Tuple[int, int]  # (x, y)
    font_size: int
    font_name: str
    color: str
    alignment: str  # 'left', 'center', 'right'
    weight: str  # 'normal', 'bold'


@dataclass
class RedesignResult:
    """重设计结果"""
    design_concept: str
    elements: List[DesignElement]
    color_scheme: Dict[str, Any]
    original_texts: List[OriginalText]
    success: bool
    error_message: str = ""


class LayoutRedesigner:
    """AI驱动的布局重设计器"""
    
    def __init__(self, api_key: str, fonts_dir: str = "fonts"):
        """
        初始化重设计器
        
        Args:
            api_key: OpenAI API密钥
            fonts_dir: 字体文件目录
        """
        self.api_key = api_key
        self.base_url = "https://openrouter.ai/api/v1"
        self.model = "openai/gpt-4o"
        self.fonts_dir = fonts_dir
        self.available_fonts = self._scan_available_fonts()
        self._ocr_instance = None
        
        print(f"布局重设计器初始化完成")
        print(f"可用字体: {len(self.available_fonts)}个")
        print(f"字体列表: {', '.join(self.available_fonts.keys())}")
    
    def _scan_available_fonts(self) -> Dict[str, str]:
        """扫描可用字体"""
        fonts = {}
        
        if not os.path.exists(self.fonts_dir):
            print(f"字体目录不存在: {self.fonts_dir}")
            return fonts
            
        # 递归扫描字体文件
        font_extensions = ['*.ttf', '*.otf', '*.ttc']
        for ext in font_extensions:
            font_files = glob.glob(os.path.join(self.fonts_dir, '**', ext), recursive=True)
            for font_file in font_files:
                # 从路径提取字体名称
                font_dir = os.path.dirname(font_file)
                font_name = os.path.basename(font_dir)
                if font_name and font_name != self.fonts_dir:
                    fonts[font_name] = font_file
                else:
                    # 如果目录名不可用，使用文件名
                    font_name = os.path.splitext(os.path.basename(font_file))[0]
                    fonts[font_name] = font_file
        
        return fonts
    
    def _init_ocr(self):
        """初始化OCR引擎，完全对齐processors/ocr_processor.py"""
        try:
            from paddleocr import PaddleOCR
            if self._ocr_instance is None:
                print("初始化PaddleOCR...")
                self._ocr_instance = PaddleOCR(
                    use_doc_orientation_classify=False,
                    use_doc_unwarping=False,
                    use_textline_orientation=False
                )
                print("PaddleOCR初始化完成")
        except ImportError:
            raise ImportError("请安装PaddleOCR: pip install paddleocr")
    
    def _is_chinese_text(self, text: str) -> bool:
        """判断是否为中文文本"""
        chinese_chars = 0
        total_chars = len(text.strip())
        
        if total_chars == 0:
            return False
            
        for char in text:
            if '\u4e00' <= char <= '\u9fff':
                chinese_chars += 1
        
        return chinese_chars / total_chars > 0.3
    
    def _get_position_description(self, bbox: Tuple[int, int, int, int], 
                                image_width: int, image_height: int) -> str:
        """获取位置描述"""
        x, y, w, h = bbox
        center_x = x + w // 2
        center_y = y + h // 2
        
        # 水平位置
        if center_x < image_width * 0.33:
            h_pos = "left"
        elif center_x > image_width * 0.67:
            h_pos = "right"
        else:
            h_pos = "center"
        
        # 垂直位置
        if center_y < image_height * 0.33:
            v_pos = "top"
        elif center_y > image_height * 0.67:
            v_pos = "bottom"
        else:
            v_pos = "middle"
        
        return f"{v_pos}-{h_pos}"
    
    def _extract_texts(self, image_path: str) -> List[OriginalText]:
        """提取图像中的文本，调用predict(input=image_path)"""
        print(f"开始OCR识别: {image_path}")
        self._init_ocr()
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
        height, width = image.shape[:2]
        ocr_results = self._ocr_instance.predict(input=image_path)
        texts = []
        text_id = 0
        # 兼容原有格式，提取rec_texts, dt_polys, rec_scores
        for res in ocr_results:
            if 'rec_texts' in res and 'dt_polys' in res:
                for text, score, poly in zip(res['rec_texts'], res.get('rec_scores', [1.0]*len(res['rec_texts'])), res['dt_polys']):
                    if score < 0.5 or not text:
                        continue
                    if not self._is_chinese_text(text):
                        continue
                    x, y, w, h = cv2.boundingRect(np.array(poly, dtype=np.int32))
                    position_desc = self._get_position_description((x, y, w, h), width, height)
                    original_text = OriginalText(
                        text=text,
                        bbox=(x, y, w, h),
                        position_desc=position_desc,
                        confidence=score
                    )
                    texts.append(original_text)
                    text_id += 1
        print(f"OCR识别完成，找到 {len(texts)} 个中文文本区域")
        for text in texts:
            print(f"  - '{text.text}' at {text.position_desc} (置信度: {text.confidence:.3f})")
        return texts
    
    def _create_text_mask(self, image: np.ndarray, texts: List[OriginalText]) -> np.ndarray:
        """创建文本掩码"""
        height, width = image.shape[:2]
        mask = np.zeros((height, width), dtype=np.uint8)
        
        for text in texts:
            x, y, w, h = text.bbox
            # 稍微扩大掩码区域，确保完全覆盖文字
            margin = 3
            x1 = max(0, x - margin)
            y1 = max(0, y - margin)
            x2 = min(width, x + w + margin)
            y2 = min(height, y + h + margin)
            
            mask[y1:y2, x1:x2] = 255
        
        return mask
    
    def _inpaint_image(self, image: np.ndarray, mask: np.ndarray) -> np.ndarray:
        """擦除文本区域"""
        print("开始图像修复...")
        
        # 使用OpenCV的图像修复算法
        inpainted = cv2.inpaint(image, mask, inpaintRadius=5, flags=cv2.INPAINT_TELEA)
        
        print("图像修复完成")
        return inpainted
    
    def _encode_image_base64(self, image_path: str) -> str:
        """将图像编码为base64"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    
    def _prepare_gpt_input(self, image_path: str, texts: List[OriginalText]) -> Dict:
        """准备发送给GPT的信息"""
        image = cv2.imread(image_path)
        height, width = image.shape[:2]
        
        # 转换文本信息
        original_texts = []
        for text in texts:
            original_texts.append({
                "text": text.text,
                "bbox": list(text.bbox),
                "position": text.position_desc,
                "confidence": text.confidence
            })
        
        input_data = {
            "image_info": {
                "width": width,
                "height": height,
                "format": "jpg"
            },
            "original_texts": original_texts,
            "available_fonts": list(self.available_fonts.keys())
        }
        
        return input_data
    
    def _call_gpt_redesign(self, input_data: Dict, image_path: str) -> Dict:
        """调用GPT-4o进行重设计"""
        print("正在调用GPT-4o进行重设计...")
        # 编码图像
        base64_image = self._encode_image_base64(image_path)
        # 构建提示词
        prompt = self._build_redesign_prompt(input_data)
        # ====== 新增调试输出 ======
        print("\n===== 发送给大模型的内容 =====")
        print("Prompt内容：\n" + prompt)
        print(f"图片base64长度: {len(base64_image)} 字符")
        print("============================\n")
        # =========================
        # 构建API请求
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 2000,
            "temperature": 0.7
        }
        try:
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=60
            )
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                print("GPT-4o重设计完成")
                print("GPT返回内容：")
                print(content)
                try:
                    design_data = json.loads(content)
                    return design_data
                except json.JSONDecodeError:
                    import re
                    json_match = re.search(r'\{.*\}', content, re.DOTALL)
                    if json_match:
                        design_data = json.loads(json_match.group())
                        return design_data
                    else:
                        raise ValueError("无法解析GPT返回的JSON格式")
            else:
                raise Exception(f"API调用失败: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"GPT API调用错误: {e}")
            raise
    
    def _parse_gpt_response(self, response: Dict) -> Tuple[List[DesignElement], str, Dict]:
        """解析GPT返回的设计方案"""
        print("解析GPT设计方案...")
        
        elements = []
        design_concept = response.get("design_concept", "")
        color_scheme = response.get("color_scheme", {})
        
        layout = response.get("layout", [])
        for item in layout:
            element = DesignElement(
                text=item.get("text", ""),
                position=tuple(item.get("position", [0, 0])),
                font_size=item.get("font_size", 24),
                font_name=item.get("font_name", ""),
                color=item.get("color", "#000000"),
                alignment=item.get("alignment", "left"),
                weight=item.get("weight", "normal")
            )
            elements.append(element)
        
        print(f"解析完成，共 {len(elements)} 个设计元素")
        for i, elem in enumerate(elements):
            print(f"  元素{i+1}: '{elem.text}' at {elem.position}, "
                  f"字体:{elem.font_name} {elem.font_size}px, 颜色:{elem.color}")
        
        return elements, design_concept, color_scheme
    
    def _load_font(self, font_name: str, size: int, weight: str = "normal") -> ImageFont.FreeTypeFont:
        """加载指定字体"""
        if font_name in self.available_fonts:
            font_path = self.available_fonts[font_name]
        else:
            # 如果指定字体不存在，使用第一个可用字体
            if self.available_fonts:
                font_path = list(self.available_fonts.values())[0]
                print(f"字体 '{font_name}' 不存在，使用默认字体")
            else:
                # 使用系统默认字体
                try:
                    return ImageFont.truetype("/System/Library/Fonts/PingFang.ttc", size)
                except:
                    return ImageFont.load_default()
        
        try:
            font = ImageFont.truetype(font_path, size)
            # TODO: 处理字体粗细，需要检查字体是否支持可变粗细
            return font
        except Exception as e:
            print(f"加载字体失败: {e}")
            return ImageFont.load_default()
    
    def _hex_to_rgb(self, hex_color: str) -> Tuple[int, int, int]:
        """将十六进制颜色转换为RGB"""
        hex_color = hex_color.lstrip('#')
        if len(hex_color) == 6:
            return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        else:
            return (0, 0, 0)  # 默认黑色
    
    def _render_design(self, base_image: np.ndarray, elements: List[DesignElement]) -> np.ndarray:
        """严格按照设计方案渲染"""
        print("开始渲染新设计...")
        
        # 转换为PIL图像
        pil_image = Image.fromarray(cv2.cvtColor(base_image, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(pil_image)
        
        for i, element in enumerate(elements):
            try:
                # 加载字体
                font = self._load_font(element.font_name, element.font_size, element.weight)
                
                # 转换颜色
                color = self._hex_to_rgb(element.color)
                
                # 计算文本位置
                x, y = element.position
                
                # 处理对齐方式
                if element.alignment == "center":
                    # 获取文本尺寸
                    bbox = draw.textbbox((0, 0), element.text, font=font)
                    text_width = bbox[2] - bbox[0]
                    x = x - text_width // 2
                elif element.alignment == "right":
                    bbox = draw.textbbox((0, 0), element.text, font=font)
                    text_width = bbox[2] - bbox[0]
                    x = x - text_width
                
                # 渲染文字
                draw.text((x, y), element.text, font=font, fill=color)
                
                print(f"  渲染元素{i+1}: '{element.text}' at ({x}, {y})")
                
            except Exception as e:
                print(f"渲染元素{i+1}失败: {e}")
                continue
        
        # 转换回OpenCV格式
        result_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        print("渲染完成")
        
        return result_image
    
    def redesign_layout(self, image_path: str, output_path: str = None) -> RedesignResult:
        """
        完整的重设计流程
        
        Args:
            image_path: 输入图像路径
            output_path: 输出图像路径，如果为None则自动生成
            
        Returns:
            RedesignResult: 重设计结果
        """
        try:
            print(f"\n=== 开始AI重设计流程 ===")
            print(f"输入图像: {image_path}")
            
            # 1. OCR提取文本
            original_texts = self._extract_texts(image_path)
            if not original_texts:
                return RedesignResult(
                    design_concept="",
                    elements=[],
                    color_scheme={},
                    original_texts=[],
                    success=False,
                    error_message="未检测到中文文本"
                )
            
            # 2. 擦除原始文本
            image = cv2.imread(image_path)
            mask = self._create_text_mask(image, original_texts)
            clean_image = self._inpaint_image(image, mask)
            
            # 保存清理后的图像用于GPT分析
            temp_clean_path = "temp_clean.jpg"
            cv2.imwrite(temp_clean_path, clean_image)
            
            # 3. 准备GPT输入
            gpt_input = self._prepare_gpt_input(image_path, original_texts)
            
            # 4. 调用GPT重设计
            gpt_response = self._call_gpt_redesign(gpt_input, image_path)
            
            # 5. 解析GPT响应
            elements, design_concept, color_scheme = self._parse_gpt_response(gpt_response)
            
            # 6. 渲染新设计
            final_image = self._render_design(clean_image, elements)
            
            # 7. 保存结果
            if output_path is None:
                name, ext = os.path.splitext(image_path)
                output_path = f"{name}_redesigned{ext}"
            
            cv2.imwrite(output_path, final_image)
            print(f"重设计结果已保存: {output_path}")
            
            # 清理临时文件
            if os.path.exists(temp_clean_path):
                os.remove(temp_clean_path)
            
            print("=== AI重设计流程完成 ===\n")
            
            return RedesignResult(
                design_concept=design_concept,
                elements=elements,
                color_scheme=color_scheme,
                original_texts=original_texts,
                success=True
            )
            
        except Exception as e:
            error_msg = f"重设计流程失败: {str(e)}"
            print(f"错误: {error_msg}")
            
            return RedesignResult(
                design_concept="",
                elements=[],
                color_scheme={},
                original_texts=[],
                success=False,
                error_message=error_msg
            )
    
    def save_debug_info(self, result: RedesignResult, debug_path: str):
        """保存调试信息"""
        debug_data = {
            "success": result.success,
            "error_message": result.error_message,
            "design_concept": result.design_concept,
            "color_scheme": result.color_scheme,
            "original_texts": [asdict(text) for text in result.original_texts],
            "design_elements": [asdict(elem) for elem in result.elements]
        }
        
        with open(debug_path, 'w', encoding='utf-8') as f:
            json.dump(debug_data, f, ensure_ascii=False, indent=2)
        
        print(f"调试信息已保存: {debug_path}")

    def _build_redesign_prompt(self, input_data: Dict) -> str:
        """构建彻底重新设计的提示词"""
        width = input_data["image_info"]["width"]
        height = input_data["image_info"]["height"]
        fonts = ", ".join(input_data["available_fonts"])
        original_texts = input_data["original_texts"]
        
        texts_desc = []
        for text_info in original_texts:
            texts_desc.append(f"'{text_info['text']}'")
        texts_str = ", ".join(texts_desc)
        
        prompt = f"""你是一位顶级的品牌视觉设计师，拥有国际4A广告公司背景。现在需要你完全重新设计这个产品的视觉传达，创造一个与原设计截然不同的高级版本。

## 🚫 重要限制：
**完全忽略原有的文字排版位置和风格，不要被原图束缚！**
- 不要模仿原图的布局方式
- 不要使用相似的字体大小关系
- 不要沿用原有的文字位置安排
- 要创造出完全不同的视觉层次

## 🎯 设计任务：
### 第一步：深度产品分析
观察产品图片，识别：
- 产品类型和核心功能
- 目标用户群体（年龄、消费层次、生活方式）
- 产品的差异化卖点
- 适合的品牌调性（温馨、科技、奢华、年轻等）

### 第二步：重新定义品牌语言
基于原文本 [{texts_str}] 的含义，创造全新的表达：
- **主标题**：一句话概括核心价值，要有冲击力和记忆点
- **副标题**：补充说明或情感诉求，增强说服力  
- **功能描述**：具体的产品特性，但要用更有吸引力的方式表达
- **品牌态度**：体现品牌personality的slogan或态度

### 第三步：创新视觉布局
摆脱传统电商设计思维，尝试：
- **非对称平衡**：打破居中对齐的常规
- **动态构图**：利用对角线、曲线等创造视觉张力
- **空间层次**：前景、中景、背景的文字层次安排
- **视觉引导**：设计用户视线的阅读路径

### 第四步：高级配色策略
- 分析产品图片的主色调
- 选择能提升品牌档次的配色
- 确保文字与背景有足够对比度
- 营造高端、现代的视觉氛围

## 🎨 创意要求：
1. **突破常规**：不要做"安全"的设计，要有创意风险
2. **品牌升级**：让这个产品看起来比原来高级2-3倍
3. **情感连接**：文案要触达用户的真实需求和情感
4. **视觉冲击**：第一眼就能抓住注意力
5. **专业质感**：体现国际品牌的设计水准

## 📐 技术参数：
- 画布尺寸：{width}x{height}像素
- 可用字体：{fonts}
- 字体大小范围：12-60px
- 颜色：使用高级的色彩搭配

## 💎 输出要求：
创造3-5个文字元素，每个都有明确的设计目的：

```json
{{
  "design_concept": "详细描述你的设计理念：为什么这样设计，想传达什么品牌感受，与原设计的差异化在哪里",
  "target_audience": "目标用户画像分析",
  "brand_positioning": "重新定义的品牌定位",
  "layout": [
    {{
      "text": "完全重新创作的文案内容",
      "purpose": "这段文字的设计目的（主标题/副标题/功能点/情感诉求等）",
      "position": [x, y],
      "font_size": 像素值,
      "font_name": "字体名称",
      "color": "#十六进制颜色",
      "alignment": "left/center/right",
      "weight": "normal/bold"
    }}
  ],
  "color_scheme": {{
    "primary": "主色调及选择理由",
    "secondary": "辅助色及选择理由", 
    "accent": "强调色及选择理由",
    "overall_mood": "整体色彩营造的情绪"
  }}
}}
```

记住：你的任务是创造一个**全新的品牌视觉识别**，而不是优化现有设计！"""
        
        return prompt


def main():
    """测试主函数"""
    # 配置
    api_key = "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"
    image_path = "example.jpg"
    
    # 创建重设计器
    redesigner = LayoutRedesigner(api_key=api_key)
    
    # 执行重设计
    result = redesigner.redesign_layout(image_path)
    
    # 保存调试信息
    if result.success:
        redesigner.save_debug_info(result, "redesign_debug.json")
        print("重设计成功完成！")
    else:
        print(f"重设计失败: {result.error_message}")


if __name__ == "__main__":
    main() 