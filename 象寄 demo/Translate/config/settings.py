"""
配置管理模块
集中管理所有配置参数和路径
"""
import os
import json
from typing import Dict, Any
from models.data_models import PipelineConfig


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, base_dir: str = None):
        """
        初始化配置管理器
        
        Args:
            base_dir: 项目根目录，默认为当前文件的上级目录
        """
        if base_dir is None:
            self.base_dir = os.path.dirname(os.path.dirname(__file__))
        else:
            self.base_dir = base_dir
            
        self._config = PipelineConfig()
        self._font_mapping = {}
        
        # 初始化配置
        self._load_font_mapping()
    
    @property
    def config(self) -> PipelineConfig:
        """获取流水线配置"""
        return self._config
    
    @property
    def font_mapping(self) -> Dict[str, str]:
        """获取字体映射"""
        return self._font_mapping.copy()
    
    def get_fonts_dir(self) -> str:
        """获取字体目录路径"""
        return os.path.join(self.base_dir, self._config.fonts_dir)
    
    def get_output_dir(self) -> str:
        """获取输出目录路径"""
        return os.path.join(self.base_dir, self._config.output_dir)
    
    def ensure_output_dir(self) -> str:
        """确保输出目录存在"""
        output_dir = self.get_output_dir()
        os.makedirs(output_dir, exist_ok=True)
        return output_dir
    
    def _load_font_mapping(self):
        """加载字体映射配置"""
        fonts_dir = self.get_fonts_dir()
        
        # 本地字体配置
        font_configs = {
            'NotoSansSC': {
                'path': 'NotoSansSC/NotoSansSC-Black.ttf',
                'name': 'Noto Sans SC Black'
            },
            'SpoqaHanSans': {
                'path': 'SpoqaHanSans/SpoqaHanSansBold.ttf', 
                'name': 'Spoqa Han Sans Bold'
            },
            '台北黑体': {
                'path': '台北黑体/TaipeiSans-Bold.ttf',
                'name': '台北黑体 TC Bold'
            },
            '思源黑体': {
                'path': '思源黑体/SourceHanSans-VF.otf',
                'name': '思源黑体'
            }
        }
        
        # 构建字体映射
        for font_key, config in font_configs.items():
            font_path = os.path.join(fonts_dir, config['path'])
            if os.path.exists(font_path):
                self._font_mapping[config['name']] = font_path
        
        # 添加通用字体名称映射
        vf_path = os.path.join(fonts_dir, '思源黑体', 'SourceHanSans-VF.otf')
        if os.path.exists(vf_path):
            common_fonts = [
                '宋体', '黑体', '楷体', '微软雅黑', '仿宋',
                'Source Han Sans', 'SourceHanSans', 'SourceHanSansCN',
                'Source Han Sans CN', 'Noto Sans CJK SC', 'Noto Sans SC'
            ]
            for font_name in common_fonts:
                self._font_mapping[font_name] = vf_path
    
    def get_llm_config(self) -> Dict[str, Any]:
        """获取当前大模型的配置"""
        provider = self._config.llm_provider
        
        if provider == "openai":
            return {
                'api_key': self._config.openai_api_key,
                'base_url': self._config.openai_base_url,
                'model': self._config.openai_model,
                'temperature': self._config.openai_temperature,
                'max_tokens': self._config.openai_max_tokens,
                'timeout': self._config.llm_timeout,
                'max_retries': self._config.llm_max_retries
            }
        else:
            raise ValueError(f"不支持的大模型提供商: {provider}")
    
    def update_config(self, **kwargs):
        """更新配置参数"""
        for key, value in kwargs.items():
            if hasattr(self._config, key):
                setattr(self._config, key, value)
            else:
                print(f"警告: 未知配置参数 {key}")
    
    def add_font_mapping(self, font_name: str, font_path: str):
        """添加字体映射"""
        if os.path.exists(font_path):
            self._font_mapping[font_name] = font_path
        else:
            print(f"警告: 字体文件不存在 {font_path}")
    
    def update_llm_provider(self, provider: str):
        """更新大模型提供商"""
        self._config.llm_provider = provider
        print(f"已切换到大模型提供商: {provider}")
    
    def get_inpaint_config(self) -> Dict[str, Any]:
        """获取图像修复配置"""
        return {
            'engine': self._config.inpaint_engine,
            **self._config.inpaint_config
        }
    
    def update_inpaint_config(self, engine: str = None, **kwargs):
        """更新图像修复配置"""
        if engine:
            self._config.inpaint_engine = engine
            print(f"已切换到图像修复引擎: {engine}")
        
        # 更新具体配置
        for key, value in kwargs.items():
            self._config.inpaint_config[key] = value
            print(f"更新图像修复配置: {key} = {value}")


# 全局配置实例
_config_manager = None

def get_config_manager() -> ConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager
