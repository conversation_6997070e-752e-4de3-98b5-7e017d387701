"""
OpenAI翻译模型 - 通过OpenRouter API调用
"""
import requests
import json
import time
from typing import Dict, Any
from .base import BaseTranslationModel, LLMTranslationResult


class OpenAITranslationModel(BaseTranslationModel):
    """OpenAI翻译模型 - 使用OpenRouter API"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化OpenAI翻译模型
        
        Args:
            config: 模型配置，包含api_key等
        """
        super().__init__(config)
        
        self.api_key = config.get('api_key', '')
        self.base_url = config.get('base_url', 'https://openrouter.ai/api/v1')
        self.model = config.get('model', 'openai/gpt-4o-mini')
        self.temperature = config.get('temperature', 0.1)
        self.max_tokens = config.get('max_tokens', 1000)
        
        if not self.api_key:
            raise ValueError("OpenAI API密钥未配置")
    
    def translate(self, text: str, source_lang: str = "zh", target_lang: str = "ja") -> LLMTranslationResult:
        """
        翻译单个文本
        
        Args:
            text: 要翻译的文本
            source_lang: 源语言代码
            target_lang: 目标语言代码
            
        Returns:
            LLMTranslationResult: 翻译结果
        """
        if not text.strip():
            return LLMTranslationResult(
                original_text=text,
                translated_text=text,
                success=True,
                confidence=1.0
            )
        
        # 构建翻译提示词
        prompt = self._build_translation_prompt(text, source_lang, target_lang)
        
        # 重试机制
        for attempt in range(self.max_retries):
            try:
                response = self._make_api_request(prompt)
                
                if response.get('error'):
                    error_msg = response['error'].get('message', '未知错误')
                    print(f"API错误 (尝试 {attempt + 1}): {error_msg}")
                    if attempt == self.max_retries - 1:
                        return LLMTranslationResult(
                            original_text=text,
                            translated_text=text,
                            success=False,
                            error=f"API错误: {error_msg}",
                            confidence=0.0
                        )
                    time.sleep(2 ** attempt)  # 指数退避
                    continue
                
                # 提取翻译结果
                translated_text = self._extract_translation(response)
                
                return LLMTranslationResult(
                    original_text=text,
                    translated_text=translated_text,
                    success=True,
                    confidence=0.9  # OpenAI模型一般质量较高
                )
                
            except Exception as e:
                print(f"翻译请求失败 (尝试 {attempt + 1}): {str(e)}")
                if attempt == self.max_retries - 1:
                    return LLMTranslationResult(
                        original_text=text,
                        translated_text=text,
                        success=False,
                        error=str(e),
                        confidence=0.0
                    )
                time.sleep(2 ** attempt)
        
        # 所有重试都失败
        return LLMTranslationResult(
            original_text=text,
            translated_text=text,
            success=False,
            error="所有重试都失败",
            confidence=0.0
        )
    
    def _build_translation_prompt(self, text: str, source_lang: str, target_lang: str) -> str:
        """构建翻译提示词"""
        lang_map = {
            'zh': '中文',
            'ja': '日文',
            'en': '英文'
        }
        
        source_name = lang_map.get(source_lang, source_lang)
        target_name = lang_map.get(target_lang, target_lang)
        
        prompt = f"""你是一个专业的翻译专家，请将以下{source_name}文本翻译成{target_name}。

要求：
1. 保持原文的语义和语调
2. 符合{target_name}的表达习惯
3. 如果是产品宣传语，要翻译得自然流畅
4. 只返回翻译结果，不要添加任何解释或标点

原文：{text}

翻译："""
        
        return prompt
    
    def _make_api_request(self, prompt: str) -> Dict[str, Any]:
        """发送API请求"""
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
            'HTTP-Referer': 'https://github.com/your-repo',  # OpenRouter要求
            'X-Title': 'Image Translation Tool'
        }
        
        data = {
            'model': self.model,
            'messages': [
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'temperature': self.temperature,
            'max_tokens': self.max_tokens
        }
        
        response = requests.post(
            f'{self.base_url}/chat/completions',
            headers=headers,
            json=data,
            timeout=self.timeout
        )
        
        return response.json()
    
    def _extract_translation(self, response: Dict[str, Any]) -> str:
        """从API响应中提取翻译结果"""
        try:
            choices = response.get('choices', [])
            if not choices:
                raise ValueError("API响应中没有choices")
            
            message = choices[0].get('message', {})
            content = message.get('content', '').strip()
            
            if not content:
                raise ValueError("API响应中没有内容")
            
            return content
            
        except Exception as e:
            raise ValueError(f"解析API响应失败: {str(e)}")
    
    def health_check(self) -> bool:
        """检查模型服务是否可用"""
        try:
            test_result = self.translate("测试", "zh", "ja")
            return test_result.success
        except Exception as e:
            print(f"健康检查失败: {str(e)}")
            return False 