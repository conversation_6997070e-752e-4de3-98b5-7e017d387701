"""
翻译模型抽象基类
"""
from abc import ABC, abstractmethod
from typing import List, Dict, Any
from dataclasses import dataclass


@dataclass
class LLMTranslationResult:
    """大模型翻译结果"""
    original_text: str
    translated_text: str
    confidence: float = 1.0
    error: str = ""
    success: bool = True


class BaseTranslationModel(ABC):
    """翻译模型抽象基类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化翻译模型
        
        Args:
            config: 模型配置字典
        """
        self.config = config
        self.model_name = config.get('model_name', 'unknown')
        self.timeout = config.get('timeout', 30)
        self.max_retries = config.get('max_retries', 3)
    
    @abstractmethod
    def translate(self, text: str, source_lang: str = "zh", target_lang: str = "ja") -> LLMTranslationResult:
        """
        翻译单个文本
        
        Args:
            text: 要翻译的文本
            source_lang: 源语言代码
            target_lang: 目标语言代码
            
        Returns:
            LLMTranslationResult: 翻译结果
        """
        pass
    
    def batch_translate(self, texts: List[str], source_lang: str = "zh", target_lang: str = "ja") -> List[LLMTranslationResult]:
        """
        批量翻译文本
        
        Args:
            texts: 要翻译的文本列表
            source_lang: 源语言代码  
            target_lang: 目标语言代码
            
        Returns:
            List[LLMTranslationResult]: 翻译结果列表
        """
        results = []
        for text in texts:
            result = self.translate(text, source_lang, target_lang)
            results.append(result)
        return results
    
    @abstractmethod
    def health_check(self) -> bool:
        """
        检查模型服务是否可用
        
        Returns:
            bool: 服务是否可用
        """
        pass
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            Dict[str, Any]: 模型信息
        """
        return {
            'model_name': self.model_name,
            'timeout': self.timeout,
            'max_retries': self.max_retries,
            'config': self.config
        } 