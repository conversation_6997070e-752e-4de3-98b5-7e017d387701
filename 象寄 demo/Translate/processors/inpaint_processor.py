"""
图像修复处理器
负责原文字去除和背景重建
集成多种修复引擎：LaMa、OpenCV、Stable Diffusion
"""
import cv2
import numpy as np
import os
from typing import List, Optional
from models.data_models import TextRegion, ProcessingResult
from config.settings import get_config_manager
from mask_inpaint.factory import InpaintModelFactory
from mask_inpaint.base import BaseInpaintModel


class InpaintProcessor:
    """图像修复处理器 - 集成多引擎支持"""
    
    def __init__(self, engine: str = None, config: dict = None):
        """
        初始化图像修复处理器

        Args:
            engine: 修复引擎名称，默认从配置获取
            config: 引擎配置，默认从配置获取
        """
        self.config_manager = get_config_manager()

        # 获取inpaint配置
        inpaint_config = self.config_manager.get_inpaint_config()
        self.engine_name = engine or inpaint_config.get('engine', 'lama')

        # 处理配置格式 - 支持嵌套和扁平化两种格式
        if 'config' in inpaint_config:
            # 嵌套格式
            self.engine_config = config or inpaint_config['config']
        else:
            # 扁平化格式 - 提取除engine外的所有配置
            flat_config = {k: v for k, v in inpaint_config.items() if k != 'engine'}
            self.engine_config = config or flat_config

        # 初始化修复模型
        self.inpaint_model: Optional[BaseInpaintModel] = None
        self._init_inpaint_model()
    
    def _init_inpaint_model(self):
        """初始化图像修复模型"""
        try:
            # 使用工厂模式创建模型，支持回退机制
            self.inpaint_model = InpaintModelFactory.create_with_fallback(
                self.engine_name,
                self.engine_config
            )

            if self.inpaint_model:
                print(f"图像修复处理器初始化完成 | 引擎: {self.inpaint_model.model_name} | 设备: {self.engine_config.get('device', 'cpu')}")
            else:
                print("警告: 图像修复引擎初始化失败，将使用基础OpenCV方法")

        except Exception as e:
            print(f"图像修复引擎初始化异常: {str(e)}")
            self.inpaint_model = None
    
    def process_inpainting(
        self, 
        image: np.ndarray, 
        chinese_regions: List[TextRegion]
    ) -> ProcessingResult:
        """
        处理图像修复，去除中文文字
        
        Args:
            image: 原始图像
            chinese_regions: 中文文字区域列表
            
        Returns:
            ProcessingResult: 包含修复后图像的处理结果
        """
        try:
            if len(chinese_regions) == 0:
                print("没有中文文字区域，跳过图像修复")
                return ProcessingResult.success_result(image.copy())
            
            print(f"开始图像修复，处理 {len(chinese_regions)} 个中文文字区域")
            
            # 创建文字掩码
            mask = self._create_text_mask(image, chinese_regions)
            
            # 执行图像修复
            inpainted_image = self._perform_inpainting(image, mask)
            
            # 保存调试图像
            if self.config_manager.config.enable_inpaint_debug:
                self._save_debug_images(image, mask, inpainted_image, chinese_regions)
            
            print("图像修复完成")
            return ProcessingResult.success_result(inpainted_image)
            
        except Exception as e:
            error_msg = f"图像修复失败: {str(e)}"
            print(error_msg)
            return ProcessingResult.error_result(error_msg)
    
    def _create_text_mask(self, image: np.ndarray, regions: List[TextRegion]) -> np.ndarray:
        """
        创建文字掩码
        
        Args:
            image: 原始图像
            regions: 文字区域列表
            
        Returns:
            np.ndarray: 文字掩码
        """
        # 创建空白掩码
        mask = np.zeros(image.shape[:2], dtype=np.uint8)
        
        # 为每个中文文字区域创建掩码
        for region in regions:
            if not region.is_chinese:
                continue
                
            # 使用多边形填充创建更精确的掩码
            points = np.array(region.poly, dtype=np.int32)
            cv2.fillPoly(mask, [points], 255)
        
        return mask
    
    def _perform_inpainting(self, image: np.ndarray, mask: np.ndarray) -> np.ndarray:
        """
        执行图像修复 - 使用新的引擎架构
        
        Args:
            image: 原始图像
            mask: 文字掩码
            
        Returns:
            np.ndarray: 修复后的图像
        """
        try:
            # 使用新的修复引擎
            result = self.inpaint_model.inpaint(image, mask)
            
            if result.success:
                print(f"图像修复完成，耗时 {result.processing_time:.2f}s "
                      f"(引擎: {result.engine_name})")
                return result.inpainted_image
            else:
                print(f"图像修复失败: {result.error_message}")
                # 回退到简单方法
                return cv2.inpaint(image, mask, 5, cv2.INPAINT_TELEA)
                
        except Exception as e:
            print(f"图像修复异常: {str(e)}")
            # 回退到OpenCV
            return cv2.inpaint(image, mask, 5, cv2.INPAINT_TELEA)
    
    def _save_debug_images(self, image: np.ndarray, mask: np.ndarray, inpainted_image: np.ndarray, chinese_regions: List[TextRegion]):
        """
        保存调试图像

        Args:
            image: 原始图像
            mask: 文字掩码
            inpainted_image: 修复后的图像
            chinese_regions: 中文文字区域列表
        """
        try:
            # 确保调试目录存在
            debug_dir = self.config_manager.ensure_debug_dir("inpaint_processor")

            # 保存文字掩码
            mask_path = os.path.join(debug_dir, "chinese_text_mask.png")
            cv2.imwrite(mask_path, mask)

            # 保存修复后的图像
            inpainted_path = os.path.join(debug_dir, "inpainted_result.png")
            cv2.imwrite(inpainted_path, inpainted_image)

            # 保存对比图像（原图和修复后图像并排）
            if image.shape == inpainted_image.shape:
                comparison = np.hstack([image, inpainted_image])
                comparison_path = os.path.join(debug_dir, "before_after_comparison.png")
                cv2.imwrite(comparison_path, comparison)

            print(f"图像修复调试图像已保存到: {debug_dir}")
            print(f"  - 掩码: {mask_path}")
            print(f"  - 修复结果: {inpainted_path}")

        except Exception as e:
            print(f"保存图像修复调试图像失败: {e}")
    

    
    def create_mask_from_polys(self, image_shape: tuple, polys: List[np.ndarray]) -> np.ndarray:
        """
        从多边形列表创建掩码
        
        Args:
            image_shape: 图像形状 (height, width)
            polys: 多边形列表
            
        Returns:
            np.ndarray: 掩码图像
        """
        mask = np.zeros(image_shape[:2], dtype=np.uint8)
        
        for poly in polys:
            points = np.array(poly, dtype=np.int32)
            cv2.fillPoly(mask, [points], 255)
        
        return mask
    
    def apply_morphological_operations(self, mask: np.ndarray, operation: str = "close") -> np.ndarray:
        """
        对掩码应用形态学操作
        
        Args:
            mask: 输入掩码
            operation: 操作类型 ("close", "open", "dilate", "erode")
            
        Returns:
            np.ndarray: 处理后的掩码
        """
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        
        if operation == "close":
            return cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        elif operation == "open":
            return cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        elif operation == "dilate":
            return cv2.dilate(mask, kernel, iterations=1)
        elif operation == "erode":
            return cv2.erode(mask, kernel, iterations=1)
        else:
            return mask
    
    def inpaint_with_different_methods(
        self, 
        image: np.ndarray, 
        mask: np.ndarray, 
        method: str = "telea"
    ) -> np.ndarray:
        """
        使用不同方法进行图像修复
        
        Args:
            image: 原始图像
            mask: 掩码
            method: 修复方法 ("telea", "ns")
            
        Returns:
            np.ndarray: 修复后的图像
        """
        if method == "telea":
            return cv2.inpaint(image, mask, 5, cv2.INPAINT_TELEA)
        elif method == "ns":
            return cv2.inpaint(image, mask, 5, cv2.INPAINT_NS)
        else:
            raise ValueError(f"不支持的修复方法: {method}")
    
    def get_inpaint_radius_recommendation(self, mask: np.ndarray) -> int:
        """
        根据掩码特征推荐修复半径
        
        Args:
            mask: 掩码图像
            
        Returns:
            int: 推荐的修复半径
        """
        # 计算掩码中连通区域的平均大小
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return 3  # 默认值
        
        # 计算平均轮廓面积
        areas = [cv2.contourArea(contour) for contour in contours]
        avg_area = sum(areas) / len(areas)
        
        # 基于面积推荐半径
        if avg_area < 100:
            return 3
        elif avg_area < 500:
            return 5
        elif avg_area < 1000:
            return 7
        else:
            return 10
