"""
翻译处理器
负责文本翻译和布局感知分组
"""
import numpy as np
import os
from typing import List, Tuple

from models.data_models import (
    TextRegion, FontMatchResult, LayoutResult, 
    TranslationResult, StyleInfo, ProcessingResult
)
from config.settings import get_config_manager
from translation_models import TranslationModelFactory


class TranslationProcessor:
    """翻译处理器"""
    
    def __init__(self, weight_adjustment: int = 400):
        """
        初始化翻译处理器
        
        Args:
            weight_adjustment: 可变字体粗细调节值 (100-900)
        """
        self.config_manager = get_config_manager()
        self.font_mapping = self.config_manager.font_mapping
        self.weight_adjustment = weight_adjustment
        
        # 字号缓存：(目标高度, 字体路径) -> 字号
        self._font_size_cache = {}
        
        # 初始化大模型翻译服务
        self._init_translation_model()
        
        print(f"翻译处理器初始化完成，使用大模型: {self.config_manager.config.llm_provider}")
    
    def _init_translation_model(self):
        """初始化翻译模型"""
        try:
            config = self.config_manager.config
            llm_config = self.config_manager.get_llm_config()
            
            self.translation_model = TranslationModelFactory.create_model(
                provider=config.llm_provider,
                config=llm_config
            )
                
        except Exception as e:
            print(f"初始化翻译模型失败: {str(e)}")
            raise
    
    def process_translation(
        self, 
        image: np.ndarray,
        regions: List[TextRegion], 
        font_results: List[FontMatchResult],
        layout_result: LayoutResult
    ) -> ProcessingResult:
        """
        处理翻译任务
        
        Args:
            image: 原始图像
            regions: 文字区域列表
            font_results: 字体匹配结果
            layout_result: 布局分析结果
            
        Returns:
            ProcessingResult: 包含TranslationResult列表的处理结果
        """
        try:
            translation_candidates = []
            
            # 创建字体结果映射
            font_map = {fr.region_id: fr for fr in font_results}
            
            for region in regions:
                if not region.is_chinese:
                    continue
                
                # 获取对应的字体信息
                font_info = font_map.get(region.id)
                if not font_info:
                    continue
                
                translation_candidates.append({
                    'region': region,
                    'font_info': font_info
                })
            
            # 批量翻译所有中文文本
            if translation_candidates:
                texts_to_translate = [item['region'].text for item in translation_candidates]
                batch_results = self.translation_model.batch_translate(texts_to_translate, source_lang="zh", target_lang="ja")
                
                final_results = []
                for i, item in enumerate(translation_candidates):
                    region = item['region']
                    font_info = item['font_info']
                    translate_result = batch_results[i] if i < len(batch_results) else None
                    
                    if not translate_result or not translate_result.success:
                        print(f"翻译失败: '{region.text}' - {translate_result.error if translate_result else '未知错误'}")
                        continue
                    
                    translated_text = translate_result.translated_text
                    if translated_text == region.text:
                        continue  # 跳过未翻译的文本
                
                    # 使用OCR阶段提供的样式信息
                    style_info = self._convert_style_info(region.style_info)
                    
                    # 创建翻译结果
                    translation_result = TranslationResult(
                        original_text=region.text,
                        translated_text=translated_text,
                        bbox=region.bbox,
                        style_info=style_info,
                        font_info=font_info,
                        group_key="",  # 将在布局感知处理中设置
                        group_scale_factor=1.0
                    )
                    
                    final_results.append(translation_result)
                    
                    print(f"翻译: '{region.text}' → '{translated_text}'")
            
                # 应用布局感知分组策略
                processed_results = self._apply_layout_aware_strategy(
                    final_results, image, layout_result
                )
                
                print(f"翻译处理完成，处理了 {len(processed_results)} 个翻译结果")
                return ProcessingResult.success_result(processed_results)
            else:
                print("没有找到需要翻译的中文文本")
                return ProcessingResult.success_result([])
            
        except Exception as e:
            error_msg = f"翻译处理失败: {str(e)}"
            print(error_msg)
            return ProcessingResult.error_result(error_msg)
    
    def _translate_text(self, text: str) -> str:
        """使用大模型翻译文本"""
        if not text.strip():
            return text
        
        try:
            # 调用大模型翻译
            result = self.translation_model.translate(text, source_lang="zh", target_lang="ja")
            
            if result.success:
                return result.translated_text
            else:
                print(f"翻译失败: {result.error}, 返回原文")
                return text
                
        except Exception as e:
            print(f"翻译异常: {str(e)}, 返回原文")
            return text
    
    def _convert_style_info(self, style_dict: dict) -> StyleInfo:
        """将OCR阶段的样式字典转换为StyleInfo对象"""
        try:
            if not style_dict:
                return self._default_style_info()

            return StyleInfo(
                estimated_font_size=style_dict.get('estimated_font_size', 24),
                precise_height=style_dict.get('precise_height', 24),
                color=style_dict.get('text_color', (0, 0, 0)),
                background_color=style_dict.get('background_color', (255, 255, 255)),
                is_bold=style_dict.get('is_bold', False),
                is_dark_text=style_dict.get('is_dark_text', True),
                contrast_ratio=style_dict.get('contrast_ratio', 21.0)
            )

        except Exception as e:
            print(f"样式信息转换失败: {e}")
            return self._default_style_info()

    def _default_style_info(self) -> StyleInfo:
        """返回默认样式信息"""
        return StyleInfo(
            estimated_font_size=24,
            precise_height=24,
            color=(0, 0, 0),
            background_color=(255, 255, 255),
            is_bold=False,
            is_dark_text=True,
            contrast_ratio=21.0
        )
    
    def _calculate_contrast_ratio(self, color1: Tuple[int, int, int], color2: Tuple[int, int, int]) -> float:
        """计算两个颜色的对比度"""
        def luminance(color):
            r, g, b = [c / 255.0 for c in color]
            r = r / 12.92 if r <= 0.03928 else ((r + 0.055) / 1.055) ** 2.4
            g = g / 12.92 if g <= 0.03928 else ((g + 0.055) / 1.055) ** 2.4
            b = b / 12.92 if b <= 0.03928 else ((b + 0.055) / 1.055) ** 2.4
            return 0.2126 * r + 0.7152 * g + 0.0722 * b
        
        l1 = luminance(color1)
        l2 = luminance(color2)
        
        lighter = max(l1, l2)
        darker = min(l1, l2)
        
        return (lighter + 0.05) / (darker + 0.05)
    
    def _apply_layout_aware_strategy(
        self, 
        translation_results: List[TranslationResult],
        image: np.ndarray,
        layout_result: LayoutResult
    ) -> List[TranslationResult]:
        """应用布局感知分组策略 - 直接使用布局分析结果"""
        try:
            print(f"\n开始布局感知分组处理...")
            
            # 直接使用布局分析的分组结果
            grouped_results = self._apply_layout_groups(translation_results, layout_result)
            
            # 为每个分组应用统一约束
            processed_results = []
            for group_key, group_results in grouped_results.items():
                print(f"\n处理分组 {group_key}:")
                
                # 应用统一约束到整个分组
                constrained_group = self._apply_unified_constraints_to_group(
                    group_results, group_key
                )
                processed_results.extend(constrained_group)
            
            print(f"\n布局感知分组处理完成，共处理 {len(processed_results)} 个结果")
            return processed_results
            
        except Exception as e:
            print(f"布局感知策略应用失败: {e}")
            # 返回原始结果
            for result in translation_results:
                result.group_key = "default"
                result.group_scale_factor = 1.0
            return translation_results
    
    def _apply_layout_groups(
        self, 
        translation_results: List[TranslationResult],
        layout_result: LayoutResult
    ) -> dict:
        """直接使用布局分析结果进行分组"""
        print(f"使用布局分析结果进行分组...")
        
        groups = {}
        processed_results = set()
        
        # 处理布局分析识别的分布组
        distribution_groups = layout_result.horizontal_alignment.get('distribution_groups', [])
        if distribution_groups:
            print(f"发现 {len(distribution_groups)} 个布局分布组")
            
            for group_idx, layout_group in enumerate(distribution_groups):
                # 根据布局组中的区域找到对应的翻译结果
                group_results = []
                for layout_region in layout_group:
                    layout_text = layout_region.get('text', '')
                    layout_bbox = layout_region.get('bbox', [0, 0, 0, 0])
                    
                    # 在翻译结果中找到匹配的区域
                    for result in translation_results:
                        if (result.original_text == layout_text and 
                            result.bbox == tuple(layout_bbox)):
                            group_results.append(result)
                            processed_results.add(id(result))
                            break
                
                if group_results:
                    # 使用分布组命名
                    group_key = f"distribution_group_{group_idx + 1}"
                    groups[group_key] = group_results
                    result_names = [r.original_text for r in group_results]
                    print(f"  分布组{group_idx+1}: {result_names}")
        
        # 处理其他对齐组
        for align_type in ['left_groups', 'center_groups', 'right_groups']:
            align_groups = layout_result.horizontal_alignment.get(align_type, [])
            for group_idx, layout_group in enumerate(align_groups):
                group_results = []
                for layout_region in layout_group:
                    layout_text = layout_region.get('text', '')
                    layout_bbox = layout_region.get('bbox', [0, 0, 0, 0])
                    
                    # 在翻译结果中找到匹配的区域
                    for result in translation_results:
                        if (result.original_text == layout_text and 
                            result.bbox == tuple(layout_bbox) and
                            id(result) not in processed_results):
                            group_results.append(result)
                            processed_results.add(id(result))
                            break
                
                if group_results:
                    group_key = f"{align_type[:-1]}_{group_idx + 1}"  # 去掉'_groups'后缀
                    groups[group_key] = group_results
                    result_names = [r.original_text for r in group_results]
                    print(f"  {align_type[:-1]}组{group_idx+1}: {result_names}")
        
        # 处理剩余的单独区域
        remaining_results = [r for r in translation_results if id(r) not in processed_results]
        for idx, result in enumerate(remaining_results):
            group_key = f"individual_{idx + 1}"
            groups[group_key] = [result]
            print(f"  单独区域{idx+1}: '{result.original_text}'")
        
        print(f"\n分组完成，共{len(groups)}个组")
        return groups
    

    
    def _apply_unified_constraints_to_group(
        self, 
        group_results: List[TranslationResult], 
        group_key: str
    ) -> List[TranslationResult]:
        """为同一组的文字应用统一约束 - 完整实现"""
        # 收集组内文本信息
        text_list = [f"'{r.original_text}'" for r in group_results]
        print(f"\n{group_key}: {', '.join(text_list)}")
        
        # 第一步：计算每个区域的初始字体大小和尺寸
        for result in group_results:
            self._calculate_initial_font_metrics(result)
        
        # 第二步：检查边框约束，找出最严格的缩放需求
        max_scale_factor = 1.0
        overflow_tolerance = 0.10  # 10%容忍度
        
        for result in group_results:
            original_width = result.bbox[2]
            translated_width = result.initial_text_width
            max_allowed_width = original_width * (1 + overflow_tolerance)
            
            if translated_width > max_allowed_width:
                required_scale = max_allowed_width / translated_width
                max_scale_factor = min(max_scale_factor, required_scale)
        
        # 第三步：应用统一的缩放因子
        if max_scale_factor < 1.0:
            print(f"  缩放因子: {max_scale_factor:.3f}")
            for result in group_results:
                result.group_scale_factor = max_scale_factor
                # 计算并保存最终字号
                result.final_font_size = int(result.initial_font_size * max_scale_factor)
                print(f"    '{result.original_text}': {result.initial_font_size}px → {result.final_font_size}px (目标高度: {result.style_info.precise_height}px)")
        else:
            print(f"  无缩放 (组内文字均在容忍范围)")
            for result in group_results:
                result.group_scale_factor = 1.0
                # 保存最终字号（无缩放）
                result.final_font_size = result.initial_font_size
                print(f"    '{result.original_text}': {result.initial_font_size}px (目标高度: {result.style_info.precise_height}px)")
        
        # 第四步：记录分组信息
        for result in group_results:
            result.group_key = group_key
        
        return group_results
    
    def _calculate_initial_font_metrics(self, result: TranslationResult):
        """计算区域的初始字体度量"""
        try:
            # 获取区域尺寸
            x, y, w, h = result.bbox
            
            # 使用精确高度匹配算法计算字体大小
            target_height = result.style_info.precise_height
            base_font_size = max(result.style_info.estimated_font_size, 16)
            
            # 计算初始字体大小
            font_size = self._calculate_font_size_by_height_matching(
                result.translated_text, target_height, result.font_info.font_path, base_font_size
            )
            
            # 计算文字尺寸
            text_width, text_height = self._get_text_dimensions(
                result.translated_text, result.font_info.font_path, font_size
            )
            
            # 存储初始计算结果
            result.initial_font_size = font_size
            result.initial_text_width = text_width
            result.initial_text_height = text_height
            
        except Exception as e:
            print(f"计算初始字体度量失败 '{result.original_text}': {e}")
            # 使用默认值
            result.initial_font_size = result.style_info.estimated_font_size
            result.initial_text_width = result.bbox[2]
            result.initial_text_height = result.bbox[3]
    
    def _calculate_font_size_by_height_matching(
        self, 
        text: str, 
        target_height: int, 
        font_path: str, 
        initial_guess: int = None
    ) -> int:
        """通过实际渲染高度匹配来计算字体大小"""
        if not font_path or not os.path.exists(font_path):
            return initial_guess or 20
        
        # 检查缓存
        cache_key = (target_height, font_path)
        if cache_key in self._font_size_cache:
            cached_size = self._font_size_cache[cache_key]
            return cached_size
        
        # 设置搜索范围
        min_size, max_size = 8, 100
        target_tolerance = 0  # 精确匹配：差值必须为0
        
        # 如果有初始估计，从该点开始二分搜索
        if initial_guess:
            test_size = initial_guess
        else:
            test_size = (min_size + max_size) // 2
        
        best_size = test_size
        best_diff = float('inf')
        iteration = 0
        max_iterations = 12
        
        while iteration < max_iterations:
            try:
                # 使用实际渲染测量高度
                actual_height = self._measure_rendered_text_height(text, font_path, test_size)
                height_diff = abs(actual_height - target_height)
                
                # 记录最佳匹配
                if height_diff < best_diff:
                    best_diff = height_diff
                    best_size = test_size
                
                # 只有差值为0才算精确匹配
                if height_diff == 0:
                    # 保存到缓存
                    self._font_size_cache[cache_key] = test_size
                    return test_size
                
                # 二分搜索调整
                if actual_height < target_height:
                    # 实际高度太小，需要增大字体
                    min_size = test_size + 1
                else:
                    # 实际高度太大，需要减小字体
                    max_size = test_size - 1
                
                # 检查搜索范围是否有效
                if min_size > max_size:
                    break
                
                test_size = (min_size + max_size) // 2
                iteration += 1
                
            except Exception as e:
                break
        
        # 保存到缓存
        self._font_size_cache[cache_key] = best_size
        return best_size
    
    def _measure_rendered_text_height(self, text: str, font_path: str, font_size: int) -> int:
        """测量渲染文字的实际高度"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            import cv2
            
            # 创建测试画布
            sample_width, sample_height = 400, 200
            test_image = Image.new('RGB', (sample_width, sample_height), 'white')
            draw = ImageDraw.Draw(test_image)
            
            # 创建字体对象
            if self.weight_adjustment != 400:
                # 可变字体设置
                font = ImageFont.truetype(font_path, font_size)
                if hasattr(font, 'set_variation_by_axes'):
                    font.set_variation_by_axes([self.weight_adjustment])
            else:
                font = ImageFont.truetype(font_path, font_size)
            
            # 在画布中心渲染文字
            text_x = sample_width // 4
            text_y = sample_height // 4
            draw.text((text_x, text_y), text, font=font, fill=(0, 0, 0))
            
            # 转换为numpy数组进行像素分析
            img_array = np.array(test_image)
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            
            # 二值化找到文字像素
            _, binary = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY_INV)
            
            # 找到文字区域的边界框
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if contours:
                # 获取最大轮廓（应该是我们的文字）
                largest_contour = max(contours, key=cv2.contourArea)
                _, _, _, h = cv2.boundingRect(largest_contour)
                return h
            else:
                # 如果找不到轮廓，回退到字体度量
                bbox = font.getbbox(text)
                return bbox[3] - bbox[1]
                
        except Exception as e:
            print(f"测量渲染文字高度失败: {e}")
            # 回退到估算值
            return font_size
    
    def _get_text_dimensions(self, text: str, font_path: str, font_size: int) -> Tuple[int, int]:
        """获取文字的精确尺寸"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            
            # 创建字体对象
            if self.weight_adjustment != 400:
                # 可变字体设置
                font = ImageFont.truetype(font_path, font_size)
                if hasattr(font, 'set_variation_by_axes'):
                    font.set_variation_by_axes([self.weight_adjustment])
            else:
                font = ImageFont.truetype(font_path, font_size)
            
            # 创建临时图像计算文字尺寸
            temp_img = Image.new('RGB', (1, 1))
            temp_draw = ImageDraw.Draw(temp_img)
            bbox = temp_draw.textbbox((0, 0), text, font=font)
            
            width = bbox[2] - bbox[0]
            height = bbox[3] - bbox[1]
            
            return width, height
            
        except Exception as e:
            print(f"获取文字尺寸失败: {e}")
            # 返回估算尺寸
            return len(text) * font_size // 2, font_size
